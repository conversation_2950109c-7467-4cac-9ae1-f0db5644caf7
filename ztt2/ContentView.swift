//
//  ContentView.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var authManager: AuthenticationManager

    var body: some View {
        Group {
            if authManager.isLoggedIn {
                // 已登录，显示主界面
                MainTabView()
                    .environment(\.managedObjectContext, viewContext)
            } else {
                // 未登录，显示登录页面
                LoginView(authManager: authManager)
            }
        }
        .onAppear {
            // 应用初始化已在ztt2App中完成，这里只需要记录状态
            if authManager.isLoggedIn {
                print("✅ ContentView加载完成，用户已登录")
            } else {
                print("✅ ContentView加载完成，等待用户登录")
            }
        }
    }
}



#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
