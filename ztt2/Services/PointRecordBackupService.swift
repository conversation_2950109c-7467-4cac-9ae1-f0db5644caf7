//
//  PointRecordBackupService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit

/**
 * 积分记录专用备份服务
 * 专门针对积分记录实现多重备份机制，确保积分数据永不丢失
 * 参考ztt1项目的成功实现
 */
@MainActor
class PointRecordBackupService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = PointRecordBackupService()
    
    // MARK: - Published Properties
    @Published var backupStatus: BackupStatus = .idle
    @Published var lastBackupDate: Date?
    @Published var totalBackupCount: Int = 0
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    
    // MARK: - Storage Keys
    private struct StorageKeys {
        // 主备份
        static let primaryBackup = "point_records_primary_backup"
        static let primaryChecksum = "point_records_primary_checksum"
        static let primaryTimestamp = "point_records_primary_timestamp"
        
        // 次备份
        static let secondaryBackup = "point_records_secondary_backup"
        static let secondaryChecksum = "point_records_secondary_checksum"
        static let secondaryTimestamp = "point_records_secondary_timestamp"
        
        // 本地备份
        static let localBackup = "local_point_records_backup"
        static let localChecksum = "local_point_records_checksum"
        static let localTimestamp = "local_point_records_timestamp"
        
        // 统计信息
        static let backupCount = "point_records_backup_count"
        static let lastBackupDate = "point_records_last_backup_date"
    }
    
    // MARK: - Initialization
    private init() {
        loadBackupStatistics()
        setupAutoBackup()
    }
    
    // MARK: - Public Methods
    
    /**
     * 执行完整的积分记录备份
     */
    func performFullBackup() async -> Bool {
        backupStatus = .backing
        
        do {
            // 1. 获取所有积分记录
            let records = try await fetchAllPointRecords()
            
            // 2. 创建备份数据
            let backupData = try createBackupData(from: records)
            
            // 3. 执行三重备份
            try await performTripleBackup(backupData)
            
            // 4. 更新统计信息
            updateBackupStatistics(recordCount: records.count)
            
            backupStatus = .success
            print("✅ 积分记录完整备份完成: \(records.count) 条记录")
            return true
            
        } catch {
            backupStatus = .failed(error)
            print("❌ 积分记录备份失败: \(error)")
            return false
        }
    }
    
    /**
     * 增量备份新增的积分记录
     */
    func performIncrementalBackup() async -> Bool {
        guard let lastBackup = lastBackupDate else {
            // 如果没有上次备份记录，执行完整备份
            return await performFullBackup()
        }
        
        backupStatus = .backing
        
        do {
            // 获取自上次备份以来的新记录
            let newRecords = try await fetchPointRecords(since: lastBackup)
            
            if newRecords.isEmpty {
                backupStatus = .success
                print("ℹ️ 没有新的积分记录需要备份")
                return true
            }
            
            // 获取现有备份并合并新记录
            let existingBackup = try await loadExistingBackup()
            let mergedRecords = existingBackup + newRecords.map { createBackupRecord(from: $0) }
            
            // 创建合并后的备份数据
            let backupData = try JSONEncoder().encode(mergedRecords)
            
            // 执行三重备份
            try await performTripleBackup(backupData)
            
            // 更新统计信息
            updateBackupStatistics(recordCount: mergedRecords.count)
            
            backupStatus = .success
            print("✅ 积分记录增量备份完成: 新增 \(newRecords.count) 条记录")
            return true
            
        } catch {
            backupStatus = .failed(error)
            print("❌ 积分记录增量备份失败: \(error)")
            return false
        }
    }
    
    /**
     * 恢复积分记录
     */
    func restorePointRecords() async -> RestoreResult {
        backupStatus = .restoring
        
        do {
            // 1. 按优先级尝试恢复
            let (backupData, source) = try await loadBestAvailableBackup()
            
            guard let data = backupData else {
                backupStatus = .failed(RestoreError.noBackupFound)
                return RestoreResult(success: false, restoredCount: 0, source: "无")
            }
            
            // 2. 解析备份数据
            let backupRecords = try JSONDecoder().decode([PointRecordBackup].self, from: data)
            
            // 3. 检查当前数据库中的记录
            let currentRecords = try await fetchAllPointRecords()
            
            // 4. 恢复缺失的记录
            var restoredCount = 0
            for backupRecord in backupRecords {
                if !recordExists(id: backupRecord.id, in: currentRecords) {
                    try restoreRecord(from: backupRecord)
                    restoredCount += 1
                }
            }
            
            // 5. 保存恢复的数据
            if restoredCount > 0 {
                coreDataManager.save()
            }
            
            backupStatus = .success
            print("✅ 积分记录恢复完成: 从\(source)恢复了 \(restoredCount) 条记录")
            
            return RestoreResult(success: true, restoredCount: restoredCount, source: source)
            
        } catch {
            backupStatus = .failed(error)
            print("❌ 积分记录恢复失败: \(error)")
            return RestoreResult(success: false, restoredCount: 0, source: "恢复失败")
        }
    }
    
    /**
     * 验证备份完整性
     */
    func verifyBackupIntegrity() async -> IntegrityReport {
        var report = IntegrityReport()
        
        // 检查主备份
        if let primaryData = ubiquitousStore.data(forKey: StorageKeys.primaryBackup),
           let primaryChecksum = ubiquitousStore.string(forKey: StorageKeys.primaryChecksum) {
            report.primaryBackupValid = (calculateChecksum(for: primaryData) == primaryChecksum)
        }

        // 检查次备份
        if let secondaryData = ubiquitousStore.data(forKey: StorageKeys.secondaryBackup),
           let secondaryChecksum = ubiquitousStore.string(forKey: StorageKeys.secondaryChecksum) {
            report.secondaryBackupValid = (calculateChecksum(for: secondaryData) == secondaryChecksum)
        }

        // 检查本地备份
        if let localData = UserDefaults.standard.data(forKey: StorageKeys.localBackup),
           let localChecksum = UserDefaults.standard.string(forKey: StorageKeys.localChecksum) {
            report.localBackupValid = (calculateChecksum(for: localData) == localChecksum)
        }

        // 比较备份数据一致性
        report.backupsConsistent = await checkBackupConsistency()
        
        return report
    }
    
    // MARK: - Private Methods
    
    /**
     * 获取所有积分记录
     */
    private func fetchAllPointRecords() async throws -> [PointRecord] {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \PointRecord.timestamp, ascending: false)]
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 获取指定时间之后的积分记录
     */
    private func fetchPointRecords(since date: Date) async throws -> [PointRecord] {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        request.predicate = NSPredicate(format: "timestamp > %@", date as NSDate)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \PointRecord.timestamp, ascending: false)]
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 创建备份数据
     */
    private func createBackupData(from records: [PointRecord]) throws -> Data {
        let backupRecords = records.map { createBackupRecord(from: $0) }
        return try JSONEncoder().encode(backupRecords)
    }
    
    /**
     * 创建备份记录
     */
    private func createBackupRecord(from record: PointRecord) -> PointRecordBackup {
        return PointRecordBackup(
            id: record.id?.uuidString ?? UUID().uuidString,
            reason: record.reason ?? "",
            value: record.value,
            timestamp: record.timestamp ?? Date(),
            recordType: record.recordType ?? "behavior",
            isReversed: record.isReversed,
            memberID: record.member?.id?.uuidString ?? ""
        )
    }
    
    /**
     * 执行三重备份
     */
    private func performTripleBackup(_ data: Data) async throws {
        let checksum = calculateChecksum(for: data)
        let timestamp = Date()
        
        // 1. 主备份到NSUbiquitousKeyValueStore
        ubiquitousStore.set(data, forKey: StorageKeys.primaryBackup)
        ubiquitousStore.set(checksum, forKey: StorageKeys.primaryChecksum)
        ubiquitousStore.set(timestamp, forKey: StorageKeys.primaryTimestamp)
        
        // 2. 次备份到NSUbiquitousKeyValueStore（不同的键）
        ubiquitousStore.set(data, forKey: StorageKeys.secondaryBackup)
        ubiquitousStore.set(checksum, forKey: StorageKeys.secondaryChecksum)
        ubiquitousStore.set(timestamp, forKey: StorageKeys.secondaryTimestamp)
        
        // 3. 本地备份到UserDefaults
        UserDefaults.standard.set(data, forKey: StorageKeys.localBackup)
        UserDefaults.standard.set(checksum, forKey: StorageKeys.localChecksum)
        UserDefaults.standard.set(timestamp, forKey: StorageKeys.localTimestamp)
        
        // 强制同步
        ubiquitousStore.synchronize()
    }
    
    /**
     * 计算数据校验和
     */
    private func calculateChecksum(for data: Data) -> String {
        return data.base64EncodedString().suffix(32).description
    }
    
    /**
     * 更新备份统计信息
     */
    private func updateBackupStatistics(recordCount: Int) {
        totalBackupCount = recordCount
        lastBackupDate = Date()
        
        UserDefaults.standard.set(recordCount, forKey: StorageKeys.backupCount)
        UserDefaults.standard.set(lastBackupDate, forKey: StorageKeys.lastBackupDate)
    }
    
    /**
     * 加载备份统计信息
     */
    private func loadBackupStatistics() {
        totalBackupCount = UserDefaults.standard.integer(forKey: StorageKeys.backupCount)
        lastBackupDate = UserDefaults.standard.object(forKey: StorageKeys.lastBackupDate) as? Date
    }
    
    /**
     * 设置自动备份
     */
    private func setupAutoBackup() {
        // 监听CoreData保存通知
        NotificationCenter.default.addObserver(
            forName: .NSManagedObjectContextDidSave,
            object: coreDataManager.viewContext,
            queue: .main
        ) { [weak self] notification in
            // 检查是否有积分记录变更
            if let insertedObjects = notification.userInfo?[NSInsertedObjectsKey] as? Set<NSManagedObject>,
               insertedObjects.contains(where: { $0 is PointRecord }) {
                Task {
                    await self?.performIncrementalBackup()
                }
            }
        }
    }

    /**
     * 加载现有备份
     */
    private func loadExistingBackup() async throws -> [PointRecordBackup] {
        let (data, _) = try await loadBestAvailableBackup()
        guard let backupData = data else {
            return []
        }
        return try JSONDecoder().decode([PointRecordBackup].self, from: backupData)
    }

    /**
     * 加载最佳可用备份
     */
    private func loadBestAvailableBackup() async throws -> (Data?, String) {
        // 1. 尝试主备份
        if let primaryData = ubiquitousStore.data(forKey: StorageKeys.primaryBackup),
           let primaryChecksum = ubiquitousStore.string(forKey: StorageKeys.primaryChecksum),
           calculateChecksum(for: primaryData) == primaryChecksum {
            return (primaryData, "主备份")
        }

        // 2. 尝试次备份
        if let secondaryData = ubiquitousStore.data(forKey: StorageKeys.secondaryBackup),
           let secondaryChecksum = ubiquitousStore.string(forKey: StorageKeys.secondaryChecksum),
           calculateChecksum(for: secondaryData) == secondaryChecksum {
            return (secondaryData, "次备份")
        }

        // 3. 尝试本地备份
        if let localData = UserDefaults.standard.data(forKey: StorageKeys.localBackup),
           let localChecksum = UserDefaults.standard.string(forKey: StorageKeys.localChecksum),
           calculateChecksum(for: localData) == localChecksum {
            return (localData, "本地备份")
        }

        return (nil, "无")
    }

    /**
     * 检查记录是否存在
     */
    private func recordExists(id: String, in records: [PointRecord]) -> Bool {
        return records.contains { $0.id?.uuidString == id }
    }

    /**
     * 恢复单条记录
     */
    private func restoreRecord(from backup: PointRecordBackup) throws {
        let record = PointRecord(context: coreDataManager.viewContext)
        record.id = UUID(uuidString: backup.id) ?? UUID()
        record.reason = backup.reason
        record.value = backup.value
        record.timestamp = backup.timestamp
        record.recordType = backup.recordType
        record.isReversed = backup.isReversed

        // 查找关联的成员
        if !backup.memberID.isEmpty,
           let memberUUID = UUID(uuidString: backup.memberID) {
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            memberRequest.predicate = NSPredicate(format: "id == %@", memberUUID as CVarArg)
            if let member = try coreDataManager.viewContext.fetch(memberRequest).first {
                record.member = member
            }
        }
    }

    /**
     * 检查备份一致性
     */
    private func checkBackupConsistency() async -> Bool {
        do {
            let (primaryData, _) = try await loadBestAvailableBackup()
            guard let data = primaryData else { return false }

            let primaryRecords = try JSONDecoder().decode([PointRecordBackup].self, from: data)

            // 检查次备份
            if let secondaryData = ubiquitousStore.data(forKey: StorageKeys.secondaryBackup),
               let secondaryChecksum = ubiquitousStore.string(forKey: StorageKeys.secondaryChecksum),
               calculateChecksum(for: secondaryData) == secondaryChecksum {

                let secondaryRecords = try JSONDecoder().decode([PointRecordBackup].self, from: secondaryData)
                return primaryRecords.count == secondaryRecords.count
            }

            return true
        } catch {
            return false
        }
    }
}

// MARK: - Data Structures

/**
 * 备份状态
 */
enum BackupStatus {
    case idle
    case backing
    case restoring
    case success
    case failed(Error)

    var displayText: String {
        switch self {
        case .idle:
            return "待备份"
        case .backing:
            return "备份中"
        case .restoring:
            return "恢复中"
        case .success:
            return "备份成功"
        case .failed(let error):
            return "备份失败: \(error.localizedDescription)"
        }
    }
}

/**
 * 恢复结果
 */
struct RestoreResult {
    let success: Bool
    let restoredCount: Int
    let source: String
}

/**
 * 完整性报告
 */
struct IntegrityReport {
    var primaryBackupValid: Bool = false
    var secondaryBackupValid: Bool = false
    var localBackupValid: Bool = false
    var backupsConsistent: Bool = false

    var isFullyValid: Bool {
        return primaryBackupValid && secondaryBackupValid && localBackupValid && backupsConsistent
    }

    var validBackupCount: Int {
        var count = 0
        if primaryBackupValid { count += 1 }
        if secondaryBackupValid { count += 1 }
        if localBackupValid { count += 1 }
        return count
    }
}

/**
 * 恢复错误
 */
enum RestoreError: Error, LocalizedError {
    case noBackupFound
    case corruptedBackup
    case restoreFailed

    var errorDescription: String? {
        switch self {
        case .noBackupFound:
            return "未找到可用的备份数据"
        case .corruptedBackup:
            return "备份数据已损坏"
        case .restoreFailed:
            return "数据恢复失败"
        }
    }
}
